# -*- coding: utf-8 -*-
"""Cybersecurity Threat Detection Projec.ipynb

Automatically generated by <PERSON>b.

Original file is located at
    https://colab.research.google.com/drive/1gt09fzCazVOH6M_X_199lisHiTXJsHcH

**New training**
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import LabelEncoder, MinMaxScaler
from sklearn.model_selection import train_test_split
from imblearn.over_sampling import SMOTE
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score, precision_score, recall_score
import tensorflow as tf
from tensorflow.keras.models import Sequential, load_model
from tensorflow.keras.layers import SimpleRNN, Dropout, Dense
import os
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

print("📥 Loading dataset...")
df = pd.read_csv("combine.csv", encoding='utf-8', low_memory=False)
df.rename(columns=lambda x: x.strip(), inplace=True)
df.drop_duplicates(inplace=True)

# Identify and handle missing values
print("🔍 Checking for missing values...")
missing_summary = df.isnull().sum()
missing_cols = missing_summary[missing_summary > 0].index.tolist()
print(f"Found missing in columns: {missing_cols}")

for col in missing_cols:
    if df[col].dtype == 'object':
        df[col].fillna(df[col].mode()[0], inplace=True)
    else:
        df[col].fillna(df[col].median(), inplace=True)

le = LabelEncoder()
df['Label'] = le.fit_transform(df['Label'])

X = df.drop('Label', axis=1)
y = df['Label']

# Convert to numeric safely
X = X.apply(pd.to_numeric, errors='coerce')
X.dropna(inplace=True)
y = y[X.index]

# Handle any infinities
X.replace([np.inf, -np.inf], np.nan, inplace=True)
X.dropna(inplace=True)
y = y[X.index]

scaler = MinMaxScaler()
X_scaled = scaler.fit_transform(X)

sm = SMOTE(random_state=42)
X_resampled, y_resampled = sm.fit_resample(X_scaled, y)

X_reshaped = np.reshape(X_resampled, (X_resampled.shape[0], 1, X_resampled.shape[1]))
X_train, X_test, y_train, y_test = train_test_split(X_reshaped, y_resampled, test_size=0.2, random_state=42)

model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])

model = Sequential([
    SimpleRNN(128, return_sequences=True, input_shape=(X_train.shape[1], X_train.shape[2])),
    Dropout(0.3),
    SimpleRNN(64),
    Dropout(0.3),
    Dense(1, activation='sigmoid')
])

model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])
print("🧠 Training model...")

history = model.fit(X_train, y_train, epochs=10, batch_size=128, validation_split=0.2)

loss, accuracy = model.evaluate(X_test, y_test)
y_pred = model.predict(X_test)
y_pred_binary = (y_pred > 0.5).astype(int)

print("\n--- Model Evaluation ---")
print(f"Accuracy: {accuracy_score(y_test, y_pred_binary):.4f}")
print(f"Precision: {precision_score(y_test, y_pred_binary):.4f}")
print(f"Recall: {recall_score(y_test, y_pred_binary):.4f}")
print("\nClassification Report:\n", classification_report(y_test, y_pred_binary))
print("\nConfusion Matrix:\n", confusion_matrix(y_test, y_pred_binary))

# 🔹 Member 3 – Threat Response & Integration
# ---------------------------
def respond_to_threat(prediction):
    if prediction == 1:
        print("⚠️ ALERT: Cyberattack detected. Initiating response protocol...")
        print("-> Isolating system\n-> Blocking IP\n-> Logging event")
    else:
        print("✅ No threat detected. System is secure.")

print("\n🔄 Simulated Threat Responses:")
for i in range(5):
    respond_to_threat(int(y_pred_binary[i]))

model.save('model.keras')
print("✅ Model saved as model.keras")

# Graphs for documentation
sns.heatmap(confusion_matrix(y_test, y_pred_binary), annot=True, fmt="d", cmap='Blues')
plt.title("Confusion Matrix")
plt.xlabel("Predicted")
plt.ylabel("Actual")
plt.show()

plt.plot(history.history['accuracy'], label='Train Accuracy')
plt.plot(history.history['val_accuracy'], label='Val Accuracy')
plt.title("Training Accuracy")
plt.xlabel("Epoch")
plt.ylabel("Accuracy")
plt.legend()
plt.show()



print("📘 Document this entire pipeline in your final report with visuals and performance metrics.")

