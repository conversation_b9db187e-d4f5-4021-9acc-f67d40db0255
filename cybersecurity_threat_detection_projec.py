# -*- coding: utf-8 -*-
"""Cybersecurity Threat Detection Projec.ipynb

Automatically generated by Colab.

Original file is located at
    https://colab.research.google.com/drive/1gt09fzCazVOH6M_X_199lisHiTXJsHcH
"""

import pandas as pd
import numpy as np
from sklearn.preprocessing import LabelEncoder, MinMaxScaler
from sklearn.model_selection import train_test_split
from tensorflow.keras.utils import to_categorical

df = pd.read_csv('/content/cyberfeddefender_dataset.csv')

df.head()

df.drop_duplicates(inplace=True)
df.dropna(inplace=True)

df.head()

df['Timestamp'] = pd.to_datetime(df['Timestamp'])
df['Hour'] = df['Timestamp'].dt.hour
df['Minute'] = df['Timestamp'].dt.minute
df['Second'] = df['Timestamp'].dt.second
df.drop('Timestamp', axis=1, inplace=True)

df.head()

categorical_cols = ['Source_IP', 'Destination_IP', 'Protocol', 'Flags', 'Attack_Type']
label_encoders = {}
for col in categorical_cols:
    le = LabelEncoder()
    df[col] = le.fit_transform(df[col])
    label_encoders[col] = le

df.head()

numerical_cols = [col for col in df.columns if col not in categorical_cols + ['Label']]
scaler = MinMaxScaler()
df[numerical_cols] = scaler.fit_transform(df[numerical_cols])

# Prepare sequences for LSTM
def create_sequences(data, sequence_length):
    sequences = []
    labels = []
    for i in range(len(data) - sequence_length):
        seq = data[i:i+sequence_length]
        label = data.iloc[i+sequence_length]['Label']
        sequences.append(seq)
        labels.append(label)
    return np.array(sequences), np.array(labels)

sequence_length = 10
X, y = create_sequences(df, sequence_length)

#split data
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout, BatchNormalization
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint

input_shape = (X_train.shape[1], X_train.shape[2])
num_classes = len(np.unique(y_train))

# Build LSTM model
model = Sequential([
    LSTM(128, input_shape=input_shape, return_sequences=True),
    BatchNormalization(),
    Dropout(0.2),

    LSTM(64, return_sequences=True),
    BatchNormalization(),
    Dropout(0.2),

    LSTM(32),
    BatchNormalization(),
    Dropout(0.2),

    Dense(64, activation='relu'),
    BatchNormalization(),
    Dropout(0.2),

    Dense(num_classes, activation='softmax')
])

optimizer = Adam(learning_rate=0.001)
model.compile(optimizer=optimizer,
              loss='sparse_categorical_crossentropy',
              metrics=['accuracy'])

callbacks = [
    EarlyStopping(monitor='val_loss', patience=5, restore_best_weights=True),
    ModelCheckpoint('best_model.h5', monitor='val_loss', save_best_only=True)
]

history = model.fit(
    X_train, y_train,
    validation_data=(X_test, y_test),
    epochs=50,
    batch_size=64,
    callbacks=callbacks,
    verbose=1
)

# Evaluate model
loss, accuracy = model.evaluate(X_test, y_test, verbose=0)
print(f'Test Accuracy: {accuracy*100:.2f}%')

# Plot training history
import matplotlib.pyplot as plt

plt.figure(figsize=(12, 4))
plt.subplot(1, 2, 1)
plt.plot(history.history['accuracy'], label='Training Accuracy')
plt.plot(history.history['val_accuracy'], label='Validation Accuracy')
plt.title('Model Accuracy')
plt.ylabel('Accuracy')
plt.xlabel('Epoch')
plt.legend()

plt.subplot(1, 2, 2)
plt.plot(history.history['loss'], label='Training Loss')
plt.plot(history.history['val_loss'], label='Validation Loss')
plt.title('Model Loss')
plt.ylabel('Loss')
plt.xlabel('Epoch')
plt.legend()
plt.show()

import tensorflow as tf
from sklearn.metrics import classification_report, confusion_matrix

# Load best model
model = tf.keras.models.load_model('best_model.h5')

# Predictions
y_pred = model.predict(X_test)
y_pred_classes = np.argmax(y_pred, axis=1)

# Classification report
print(classification_report(y_test, y_pred_classes))

# Confusion matrix
cm = confusion_matrix(y_test, y_pred_classes)
print("Confusion Matrix:")
print(cm)

# Real-time detection function
def detect_threats(new_data):
    """
    Function to detect threats in real-time
    """
    # Preprocess new data (same steps as training data)
    new_data = preprocess_new_data(new_data)

    # Create sequence
    seq = create_sequences(new_data, sequence_length)

    # Predict
    prediction = model.predict(seq)
    threat_prob = prediction[0][1]  # Probability of being a threat

    # Threshold for alerting
    if threat_prob > 0.7:
        return "ALERT: Potential threat detected with confidence {:.2f}%".format(threat_prob*100)
    else:
        return "No significant threat detected"

print(model.input_shape)



import pickle
from datetime import datetime

# Create preprocessing dictionary with all necessary objects
preprocessing = {
    'scaler': scaler,  # Your MinMaxScaler
    'label_encoders': label_encoders,  # Dictionary of LabelEncoders
    'sequence_length': sequence_length,  # Your sequence length (10)
    'categorical_cols': categorical_cols,  # List of categorical columns
    'numerical_cols': numerical_cols,  # List of numerical columns
    'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")  # When it was saved
}

# Save to preprocessing.pkl
with open('preprocessing.pkl', 'wb') as f:
    pickle.dump(preprocessing, f)

print("preprocessing.pkl saved successfully!")
print(f"Contains: {list(preprocessing.keys())}")

class ThreatDetector:
    def __init__(self, model_path):
        self.model = tf.keras.models.load_model(model_path)
        self.scaler = scaler  # Load the saved scaler
        self.label_encoders = label_encoders  # Load the saved label encoders
        self.sequence_length = sequence_length

    def preprocess_data(self, data):
        # Example minimal preprocessing (replace with your real logic)
        processed_data = np.array(data).reshape(1, -1)
        return processed_data

    def create_sequence(self, data):
        # Simply return the data for now; modify if you use time-series/RNN models
        return data

    def detect(self, network_data):
        # Preprocess
        processed_data = self.preprocess_data(network_data)

        # Create sequence (batch shape)
        seq = self.create_sequence(processed_data)

        # Predict
        prediction = self.model.predict(seq)
        return prediction

    def send_alert(self, alert_message):
        try:
            siem_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            siem_socket.connect(('siem_server', 514))
            alert = {
                'timestamp': str(datetime.now()),
                'message': alert_message,
                'severity': 'high'
            }
            siem_socket.send(json.dumps(alert).encode())
            siem_socket.close()
        except Exception as e:
            print(f"Error sending alert to SIEM: {e}")

from sklearn.ensemble import IsolationForest

# Add anomaly detection as a secondary layer
def train_anomaly_detector(X_train):
    # Flatten sequences for anomaly detection
    X_train_flat = X_train.reshape(X_train.shape[0], -1)

    # Train isolation forest
    clf = IsolationForest(n_estimators=100, contamination=0.01, random_state=42)
    clf.fit(X_train_flat)
    return clf

anomaly_detector = train_anomaly_detector(X_train)

def combined_detection(network_data):
    # LSTM prediction
    lstm_pred = detector.detect(network_data)

    # Anomaly detection
    flat_data = network_data.reshape(1, -1)
    anomaly_score = anomaly_detector.decision_function(flat_data)

    # Combined decision
    if lstm_pred > 0.7 or anomaly_score < -0.5:
        return "ALERT: Threat detected"
    else:
        return "Normal traffic"

