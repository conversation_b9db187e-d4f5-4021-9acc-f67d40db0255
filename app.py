# Cybersecurity Threat Detection User Interface using Streamlit

import streamlit as st
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import LabelEncoder, MinMaxScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score, precision_score, recall_score
from imblearn.over_sampling import SMOTE
import tensorflow as tf
from tensorflow.keras.models import Sequential, load_model
from tensorflow.keras.layers import SimpleRNN, Dropout, Dense
import os

st.set_page_config(page_title="Cybersecurity Threat Detection UI")
st.title("🔐 Cybersecurity Threat Detection Interface")

# ---------------------------
# 📥 Data Loading & Preprocessing
# ---------------------------
st.subheader("📊 Step 1: Data Preprocessing")

st.markdown("**Note:** You can upload a file up to 600MB.")

uploaded_file = st.file_uploader("Upload combine.csv file", type=["csv"], accept_multiple_files=False, key=None, help="Maximum file size: 600MB")

if uploaded_file is not None:
    df = pd.read_csv(uploaded_file, encoding='utf-8', low_memory=False)
    df.rename(columns=lambda x: x.strip(), inplace=True)
    df.drop_duplicates(inplace=True)

    missing_cols = df.columns[df.isnull().any()].tolist()
    for col in missing_cols:
        if df[col].dtype == 'object':
            df[col].fillna(df[col].mode()[0], inplace=True)
        else:
            df[col].fillna(df[col].median(), inplace=True)

    le = LabelEncoder()
    df['Label'] = le.fit_transform(df['Label'])

    X = df.drop('Label', axis=1)
    y = df['Label']

    X = X.apply(pd.to_numeric, errors='coerce')
    X.replace([np.inf, -np.inf], np.nan, inplace=True)
    X.dropna(inplace=True)
    y = y[X.index]

    scaler = MinMaxScaler()
    X_scaled = scaler.fit_transform(X)

    sm = SMOTE(random_state=42)
    X_resampled, y_resampled = sm.fit_resample(X_scaled, y)

    X_reshaped = np.reshape(X_resampled, (X_resampled.shape[0], 1, X_resampled.shape[1]))
    X_train, X_test, y_train, y_test = train_test_split(X_reshaped, y_resampled, test_size=0.2, random_state=42)

    # ---------------------------
    # 🧠 Model Training
    # ---------------------------
    st.subheader("🧠 Step 2: Model Training")
    if st.button("Start Training"):
        model = Sequential([
            SimpleRNN(128, return_sequences=True, input_shape=(X_train.shape[1], X_train.shape[2])),
            Dropout(0.3),
            SimpleRNN(64),
            Dropout(0.3),
            Dense(1, activation='sigmoid')
        ])

        model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])

        with st.spinner("Training in progress..."):
            history = model.fit(X_train, y_train, epochs=10, batch_size=128, validation_split=0.2, verbose=0)

        loss, accuracy = model.evaluate(X_test, y_test)
        y_pred = model.predict(X_test)
        y_pred_binary = (y_pred > 0.5).astype(int)

        st.success(f"✅ Model Trained | Accuracy: {accuracy_score(y_test, y_pred_binary):.4f}")

        # Show classification report
        st.code(classification_report(y_test, y_pred_binary), language='text')

        # Confusion Matrix
        fig1, ax1 = plt.subplots()
        sns.heatmap(confusion_matrix(y_test, y_pred_binary), annot=True, fmt="d", cmap='Blues', ax=ax1)
        st.pyplot(fig1)

        # Accuracy Graph
        fig2, ax2 = plt.subplots()
        ax2.plot(history.history['accuracy'], label='Train Accuracy')
        ax2.plot(history.history['val_accuracy'], label='Validation Accuracy')
        ax2.set_title("Training Accuracy")
        ax2.set_xlabel("Epoch")
        ax2.set_ylabel("Accuracy")
        ax2.legend()
        st.pyplot(fig2)

        # Save model
        model.save("model.keras")
        st.info("Model saved as 'model.keras'")

        # ---------------------------
        # 🛡️ Step 3: Threat Response Simulation
        # ---------------------------
        st.subheader("🛡️ Simulate Threat Response")
        sample_index = st.slider("Select Sample Index", 0, len(X_test) - 1, 0)
        prediction = int(y_pred_binary[sample_index])
        actual = int(y_test[sample_index])

        st.write(f"Prediction: {'🚨 Attack' if prediction == 1 else '✅ Normal'}")
        st.write(f"Actual: {'🚨 Attack' if actual == 1 else '✅ Normal'}")

        if prediction == 1:
            st.error("⚠️ Cyberattack detected.\n- Isolating system\n- Blocking IP\n- Logging event")
        else:
            st.success("✅ System is secure. No threat detected.")

else:
    st.warning("Please upload the 'combine.csv' file to continue.")



