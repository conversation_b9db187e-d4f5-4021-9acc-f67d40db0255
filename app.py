import streamlit as st
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import json
import pickle
from datetime import datetime
from sklearn.metrics import confusion_matrix, classification_report
from tensorflow.keras.models import load_model
from tensorflow.keras.initializers import Orthogonal

# Set page config
st.set_page_config(
    page_title="CyberThreat Detector",
    page_icon="🛡️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for styling
st.markdown("""
<style>
    .main {
        background-color: #0E1117;
    }
    .sidebar .sidebar-content {
        background-color: #1a1a2e;
    }
    h1, h2, h3, h4, h5, h6 {
        color: #4CAF50 !important;
    }
    .st-bq {
        border-left: 5px solid #4CAF50;
    }
    .metric-card {
        background-color: #16213E;
        border-radius: 10px;
        padding: 15px;
        margin: 10px 0;
        box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);
    }
    .alert-danger {
        background-color: #ff4444;
        color: white;
        padding: 10px;
        border-radius: 5px;
    }
    .alert-safe {
        background-color: #00C851;
        color: white;
        padding: 10px;
        border-radius: 5px;
    }
</style>
""", unsafe_allow_html=True)

# Solution for the initializer error
import tensorflow as tf
from tensorflow.keras.layers import LSTM
from tensorflow.keras import initializers

# Register custom objects
custom_objects = {
    'Orthogonal': Orthogonal,
}

# Load model and preprocessing objects
@st.cache_resource
def load_resources():
    try:
        model = load_model('best_model.h5', custom_objects=custom_objects)
    except Exception as e:
        st.error(f"Error loading model: {str(e)}")
        model = None
    
    try:
        with open('preprocessing.pkl', 'rb') as f:
            preprocessing = pickle.load(f)
    except Exception as e:
        st.error(f"Error loading preprocessing objects: {str(e)}")
        preprocessing = None
    
    return model, preprocessing

model, preprocessing = load_resources()

if model is None or preprocessing is None:
    st.error("Failed to load required resources. Please check the model and preprocessing files.")
    st.stop()

scaler = preprocessing['scaler']
label_encoders = preprocessing['label_encoders']
sequence_length = preprocessing['sequence_length']

# Sample data generator
def generate_sample_traffic():
    return {
        'Source_IP': '192.168.' + '.'.join(map(str, np.random.randint(0, 255, 2))),
        'Destination_IP': '10.0.' + '.'.join(map(str, np.random.randint(0, 255, 2))),
        'Protocol': np.random.choice(['TCP', 'UDP', 'ICMP']),
        'Packet_Length': np.random.randint(100, 2000),
        'Duration': round(np.random.uniform(0.1, 5.0), 2),
        'Source_Port': np.random.randint(1024, 65535),
        'Destination_Port': np.random.choice([80, 443, 22, 53, 3389]),
        'Bytes_Sent': np.random.randint(100, 5000),
        'Bytes_Received': np.random.randint(100, 5000),
        'Flags': np.random.choice(['SYN', 'ACK', 'FIN', 'PSH', 'RST']),
        'Flow_Packets/s': round(np.random.uniform(10, 100), 1),
        'Flow_Bytes/s': round(np.random.uniform(500, 5000), 1),
        'Avg_Packet_Size': np.random.randint(200, 1500),
        'Total_Fwd_Packets': np.random.randint(1, 50),
        'Total_Bwd_Packets': np.random.randint(1, 50),
        'Fwd_Header_Length': np.random.randint(20, 512),
        'Bwd_Header_Length': np.random.randint(20, 512),
        'Sub_Flow_Fwd_Bytes': np.random.randint(100, 5000),
        'Sub_Flow_Bwd_Bytes': np.random.randint(100, 5000),
        'Inbound': np.random.randint(0, 2),
        'Attack_Type': np.random.choice(['Normal', 'DDoS', 'Ransomware', 'Brute Force']),
        'Hour': datetime.now().hour,
        'Minute': datetime.now().minute,
        'Second': datetime.now().second
    }

# Preprocessing function
def preprocess_data(raw_data):
    df = pd.DataFrame([raw_data])
    
    # Encode categorical features
    categorical_cols = ['Source_IP', 'Destination_IP', 'Protocol', 'Flags', 'Attack_Type']
    for col in categorical_cols:
        le = label_encoders.get(col)
        if le:
            df[col] = df[col].apply(lambda x: x if x in le.classes_ else 'unknown')
            if 'unknown' not in le.classes_:
                le.classes_ = np.append(le.classes_, 'unknown')
            df[col] = le.transform(df[col])
    
    # Scale numerical features
    numerical_cols = [col for col in df.columns if col not in categorical_cols + ['Label']]
    df[numerical_cols] = scaler.transform(df[numerical_cols])
    
    return df.values

# Threat detection function
def detect_threat(data):
    processed = preprocess_data(data)
    # Pad or truncate to sequence length
    if len(processed) < sequence_length:
        padding = np.zeros((sequence_length - len(processed), processed.shape[1]))
        processed = np.vstack([padding, processed])
    elif len(processed) > sequence_length:
        processed = processed[-sequence_length:]
    
    processed = processed.reshape(1, sequence_length, -1)
    prediction = model.predict(processed)
    return float(prediction[0][1])

# Sidebar
with st.sidebar:
    st.title("🛡️ CyberThreat Detector")
    st.markdown("""
    **Real-time AI-powered network threat detection system**
    
    Uses LSTM deep learning model to identify:
    - DDoS attacks
    - Ransomware
    - Brute force attempts
    - Other malicious activities
    """)
    
    st.markdown("---")
    st.markdown("### Model Performance")
    st.metric("Accuracy", "95.2%")
    st.metric("False Positive Rate", "1.8%")
    st.metric("Detection Speed", "23ms")
    
    st.markdown("---")
    if st.button("Generate Sample Traffic"):
        st.session_state.sample_data = generate_sample_traffic()
    
    st.markdown("---")
    st.markdown("Developed by TAYYEB ULLAH")
    st.markdown("v1.0 | © 2023")

# Main content
st.title("Cybersecurity Threat Detection Dashboard")

# Dashboard layout
col1, col2 = st.columns([2, 1])

with col1:
    st.header("Real-time Network Traffic Analysis")
    
    if 'sample_data' not in st.session_state:
        st.session_state.sample_data = generate_sample_traffic()
    
    # Display sample data
    with st.expander("View Network Traffic Sample", expanded=True):
        st.json(st.session_state.sample_data)
    
    # Threat detection
    threat_prob = detect_threat(st.session_state.sample_data)
    threat_level = "HIGH" if threat_prob > 0.7 else "MEDIUM" if threat_prob > 0.4 else "LOW"
    
    if threat_prob > 0.7:
        st.markdown(f"""
        <div class="alert-danger">
            <h3>🚨 Threat Detected: {threat_level} confidence ({threat_prob*100:.1f}%)</h3>
            <p>Recommended action: Block source IP and investigate further</p>
        </div>
        """, unsafe_allow_html=True)
    else:
        st.markdown(f"""
        <div class="alert-safe">
            <h3>✅ No Threat Detected: {threat_level} risk ({threat_prob*100:.1f}%)</h3>
            <p>Network traffic appears normal</p>
        </div>
        """, unsafe_allow_html=True)
    
    # Generate new sample button
    if st.button("Analyze New Traffic Sample"):
        st.session_state.sample_data = generate_sample_traffic()
        st.experimental_rerun()

with col2:
    st.header("Threat Metrics")
    
    # Metrics cards
    st.markdown("""
    <div class="metric-card">
        <h4>📊 Today's Alerts</h4>
        <h2>42</h2>
        <p>+5 from yesterday</p>
    </div>
    """, unsafe_allow_html=True)
    
    st.markdown("""
    <div class="metric-card">
        <h4>🛡️ Prevented Attacks</h4>
        <h2>18</h2>
        <p>+3 from yesterday</p>
    </div>
    """, unsafe_allow_html=True)
    
    st.markdown("""
    <div class="metric-card">
        <h4>⏱️ Avg Response Time</h4>
        <h2>1.2s</h2>
        <p>-0.3s from last week</p>
    </div>
    """, unsafe_allow_html=True)

# Model performance section
st.markdown("---")
st.header("Model Performance Metrics")

# Confusion matrix
st.subheader("Confusion Matrix")
# Sample confusion matrix data
cm = np.array([[950, 20], [15, 1015]])
fig, ax = plt.subplots(figsize=(8, 6))
sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
            xticklabels=['Predicted Normal', 'Predicted Threat'],
            yticklabels=['Actual Normal', 'Actual Threat'], ax=ax)
ax.set_title('Model Classification Performance')
st.pyplot(fig)

# Classification report
st.subheader("Classification Report")
report = """
              precision    recall  f1-score   support

           0       0.98      0.98      0.98       970
           1       0.98      0.99      0.98      1030

    accuracy                           0.98      2000
   macro avg       0.98      0.98      0.98      2000
weighted avg       0.98      0.98      0.98      2000
"""
st.code(report, language='text')

# Threat type distribution
st.subheader("Threat Type Distribution")
threat_data = pd.DataFrame({
    'Threat Type': ['DDoS', 'Ransomware', 'Brute Force', 'Normal'],
    'Count': [320, 180, 150, 1350]
})
fig2, ax2 = plt.subplots(figsize=(10, 6))
sns.barplot(x='Threat Type', y='Count', data=threat_data, palette='viridis', ax=ax2)
ax2.set_title('Distribution of Detected Threat Types')
st.pyplot(fig2)

# Real-time monitoring section
st.markdown("---")
st.header("Real-time Monitoring")

# Create tabs for different views
tab1, tab2, tab3 = st.tabs(["Threat Map", "Traffic Flow", "Alert Log"])

with tab1:
    st.subheader("Global Threat Map")
    # Placeholder for a threat map visualization
    st.image("https://www.kaspersky.com/content/en-global/images/repository/isc/2017-images/cyberthreat-real-time-map.jpg",
             caption="Live global threat visualization (simulated)")
    
with tab2:
    st.subheader("Network Traffic Flow")
    # Sample network traffic data
    traffic_data = pd.DataFrame({
        'Time': pd.date_range(start='2023-01-01', periods=24, freq='H'),
        'Packets': np.random.poisson(500, 24) * np.linspace(1, 1.5, 24)
    })
    fig3, ax3 = plt.subplots(figsize=(10, 4))
    sns.lineplot(x='Time', y='Packets', data=traffic_data, ax=ax3)
    ax3.set_title('Network Traffic Volume (Last 24 Hours)')
    ax3.set_ylabel('Packets per second')
    st.pyplot(fig3)
    
with tab3:
    st.subheader("Recent Security Alerts")
    # Sample alert data
    alerts = pd.DataFrame({
        'Timestamp': [
            datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            (datetime.now() - pd.Timedelta(minutes=15)).strftime('%Y-%m-%d %H:%M:%S'),
            (datetime.now() - pd.Timedelta(hours=1)).strftime('%Y-%m-%d %H:%M:%S'),
            (datetime.now() - pd.Timedelta(hours=3)).strftime('%Y-%m-%d %H:%M:%S')
        ],
        'Source IP': ['************', '**********', '***********', '*************'],
        'Threat Type': ['DDoS', 'Brute Force', 'Ransomware', 'Port Scan'],
        'Severity': ['High', 'Medium', 'High', 'Low']
    })
    st.dataframe(alerts, hide_index=True, use_container_width=True)

# Footer
st.markdown("---")
st.markdown("""
<style>
.footer {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    background-color: #16213E;
    color: white;
    text-align: center;
    padding: 10px;
}
</style>
<div class="footer">
    <p>CyberThreat Detector v1.0 | Last updated: {}</p>
</div>
""".format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), unsafe_allow_html=True)